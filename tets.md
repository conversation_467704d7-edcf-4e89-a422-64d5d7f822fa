# 2025.08.11 14:00 - 校招 - 梁琦坤


### 第二部分：前端基础知识 (25分钟)

#### JavaScript基础 (10分钟)

**1. 数据类型与原型链**

**问题**: JavaScript有哪些数据类型？如何判断数据类型？

**标准答案**:
- **基本数据类型**: Number、String、Boolean、Undefined、Null、Symbol、BigInt
- **引用数据类型**: Object（包括Array、Function、Date等）
- **类型判断方法**:
  - `typeof`: 适用于基本类型（注意typeof null === 'object'）
  - `instanceof`: 判断对象类型
  - `Object.prototype.toString.call()`: 最准确的方法
  - `Array.isArray()`: 专门判断数组

**评分标准**:
- 优秀：能说出所有类型，了解ES6新增的Symbol和BigInt，掌握多种判断方法
- 良好：能说出主要类型，知道typeof和instanceof的区别
- 一般：只知道基本的几种类型和typeof方法

---

**问题**: 解释一下原型链，prototype和__proto__的区别？

**标准答案**:
- **原型链**: 每个对象都有一个__proto__属性指向其构造函数的prototype，形成链式结构用于属性查找
- **prototype**: 函数特有的属性，指向原型对象，包含共享的属性和方法
- **__proto__**: 对象的属性，指向构造函数的prototype
- **查找机制**: 对象访问属性时，先查找自身，找不到沿着__proto__向上查找直到Object.prototype

---

**2. 异步编程**

**问题**: Promise、async/await的区别和使用场景？

**标准答案**:
- **Promise**:
  - 解决回调地狱问题
  - 三种状态：pending、fulfilled、rejected
  - 支持链式调用，通过.then()和.catch()处理
  - 适合单个异步操作或简单的链式操作

- **async/await**:
  - 基于Promise的语法糖，让异步代码看起来像同步代码
  - async函数返回Promise
  - await只能在async函数中使用
  - 适合复杂的异步流程控制

```javascript
// Promise方式
function fetchUser(id) {
  return fetch(`/api/user/${id}`)
    .then(response => response.json())
    .then(user => {
      return fetch(`/api/posts/${user.id}`);
    })
    .then(response => response.json());
}

// async/await方式
async function fetchUser(id) {
  try {
    const userResponse = await fetch(`/api/user/${id}`);
    const user = await userResponse.json();
    const postsResponse = await fetch(`/api/posts/${user.id}`);
    return await postsResponse.json();
  } catch (error) {
    console.error('Error:', error);
  }
}
```

---

**问题**: 你提到用fetch API，能说说fetch和XMLHttpRequest的区别吗？

**标准答案**:
- **fetch优势**:
  - 基于Promise，支持async/await
  - 语法更简洁现代
  - 支持Request/Response对象
  - 更好的错误处理机制
  - 支持流式处理

- **XMLHttpRequest特点**:
  - 基于回调函数
  - 浏览器兼容性更好
  - 支持更多配置选项
  - 可以监听上传进度

- **使用场景**:
  - 现代项目优先使用fetch
  - 需要上传进度监听时使用XMLHttpRequest
  - 兼容老浏览器时使用XMLHttpRequest

---

**问题**: let、const、var的区别？

**标准答案**:
- **作用域**:
  - var: 函数作用域或全局作用域
  - let/const: 块级作用域

- **变量提升**:
  - var: 存在变量提升，可在声明前使用（值为undefined）
  - let/const: 存在提升但有暂时性死区，不能在声明前使用

- **重复声明**:
  - var: 允许重复声明
  - let/const: 不允许在同一作用域重复声明

- **重新赋值**:
  - var/let: 可以重新赋值
  - const: 不能重新赋值（对象属性可以修改）

```javascript
// 块级作用域示例
for (let i = 0; i < 3; i++) {
  setTimeout(() => console.log(i), 100); // 输出 0,1,2
}

for (var i = 0; i < 3; i++) {
  setTimeout(() => console.log(i), 100); // 输出 3,3,3
}
```

---

#### Vue.js框架知识 (10分钟)

**1. Vue3新特性**

**问题**: Composition API相比Options API有什么优势？

**标准答案**:
- **逻辑复用**: 更好的逻辑抽象和复用，可以将相关逻辑组织在一起
- **TypeScript支持**: 更好的类型推断和类型安全
- **性能**: 更小的打包体积，按需引入
- **代码组织**: 复杂组件的逻辑可以按功能分组，而不是按选项类型分组

---

**问题**: Vue3的响应式原理是什么？和Vue2有什么区别？

**标准答案**:
- **Vue2**: 使用Object.defineProperty劫持对象属性
  - 无法检测数组索引和长度变化
  - 无法检测对象属性的添加和删除
  - 需要递归遍历所有属性

- **Vue3**: 使用Proxy代理整个对象
  - 可以检测所有类型的变化
  - 性能更好，惰性初始化
  - 支持Map、Set、Array等更多数据类型

---

**2. 生命周期**

**问题**: Vue3的生命周期钩子有哪些？onMounted和onUnmounted的使用场景？

**标准答案**:
- **Vue3生命周期钩子**:
  - onBeforeMount: 组件挂载前
  - onMounted: 组件挂载后
  - onBeforeUpdate: 组件更新前
  - onUpdated: 组件更新后
  - onBeforeUnmount: 组件卸载前
  - onUnmounted: 组件卸载后

- **onMounted使用场景**:
  - DOM操作
  - 第三方库初始化
  - 数据请求
  - 事件监听器添加

- **onUnmounted使用场景**:
  - 清理定时器
  - 移除事件监听器
  - 取消网络请求
  - 清理第三方库实例

---

**问题**: 组件销毁时需要注意什么？

**标准答案**:
- **内存泄漏防止**:
  - 清理定时器和延时器
  - 移除事件监听器
  - 取消未完成的网络请求
  - 销毁第三方库实例

- **资源清理**:
  - 清理WebSocket连接
  - 停止动画
  - 清理观察者模式的订阅

---

**3. 性能优化**

**问题**: Vue中如何进行性能优化？

**标准答案**:
- **代码层面**:
  - 使用v-memo缓存模板
  - 合理使用computed和watch
  - 避免在模板中使用复杂表达式
  - 使用异步组件和懒加载

- **构建优化**:
  - 代码分割和懒加载
  - Tree-shaking移除未使用代码
  - 组件库按需引入

- **运行时优化**:
  - 使用Object.freeze冻结不变数据
  - 避免不必要的组件更新
  - 使用虚拟滚动处理大列表

```javascript
// v-memo 示例
<template>
  <div v-memo="[user.id]">
    <h3>{{ user.name }}</h3>
    <p>{{ user.bio }}</p>
  </div>
</template>

// 异步组件
const AsyncComponent = defineAsyncComponent(() => 
  import('./components/HeavyComponent.vue')
);
```

---

**问题**: v-if和v-show的区别和使用场景？

**标准答案**:
- **v-if**:
  - 条件性渲染，false时元素不会渲染到DOM
  - 有更高的切换开销
  - 适合条件很少改变的场景
  - 支持template标签

- **v-show**:
  - 始终渲染，通过CSS display属性控制显示
  - 有更高的初始渲染开销
  - 适合需要频繁切换的场景
  - 不支持template标签

**使用场景**:
- 权限控制、路由条件等用v-if
- 标签页切换、模态框等用v-show

---

#### TypeScript知识 (5分钟)

**问题**: TypeScript的优势是什么？

**标准答案**:
- **类型安全**: 编译时发现错误，减少运行时错误
- **更好的IDE支持**: 智能提示、重构、导航
- **代码可读性**: 类型即文档，增强代码可维护性
- **团队协作**: 统一的类型规范，降低沟通成本
- **渐进式**: 可以逐步添加类型，兼容JavaScript

---

**问题**: interface和type的区别？

**标准答案**:
- **interface特点**:
  - 只能定义对象类型
  - 支持声明合并
  - 支持继承（extends）
  - 更适合定义对象结构

- **type特点**:
  - 可以定义任何类型（联合类型、交叉类型等）
  - 不支持声明合并
  - 支持计算属性
  - 更灵活，功能更强大

```typescript
// interface 示例
interface User {
  name: string;
  age: number;
}

interface User {  // 声明合并
  email: string;
}

// type 示例
type Status = 'loading' | 'success' | 'error';
type UserWithStatus = User & { status: Status };
```

---

**问题**: 泛型的使用场景？

**标准答案**:
- **函数泛型**: 保持类型安全的同时提供灵活性
- **组件泛型**: Vue组件的props类型定义
- **工具类型**: 创建可重用的类型操作

```typescript
// 函数泛型
function identity<T>(arg: T): T {
  return arg;
}

// 组件泛型
interface ListProps<T> {
  items: T[];
  onSelect: (item: T) => void;
}

// 工具类型
type Partial<T> = {
  [P in keyof T]?: T[P];
};
```

---

### 第三部分：项目经验深入 (20分钟)

#### 针对ELIS系统开发项目

**1. 项目架构理解**

**问题**: 你提到了"采集端-本地云-远端云三级架构"，能详细解释一下这个架构的设计思路吗？

**考察重点**: 对复杂系统架构的理解能力

**期望答案**:
- **采集端**: 负责数据采集，可能包括各种传感器和设备
- **本地云**: 边缘计算节点，进行数据预处理和缓存
- **远端云**: 中央服务器，负责数据存储、分析和决策
- **设计优势**: 减少网络延迟、提高系统可靠性、支持离线工作

**问题**: 前端在这个架构中承担什么角色？

**期望答案**:
- 数据可视化界面
- 系统监控和管理
- 用户交互和操作控制
- 多级数据展示和切换

---

**2. 技术栈深入**

**问题**: 为什么选择Vue3+Vite+TypeScript这个技术组合？相比Vue2有什么优势？

**标准答案**:
- **Vue3优势**:
  - Composition API提供更好的逻辑复用
  - 性能提升（更小的包体积，更快的渲染）
  - 更好的TypeScript支持
  - Fragment支持，template可以有多个根节点

- **Vite优势**:
  - 冷启动速度快（基于ESM）
  - 热更新效率高
  - 开箱即用的TypeScript支持
  - 更好的开发体验

- **TypeScript优势**:
  - 类型安全，减少运行时错误
  - 更好的IDE支持和代码提示
  - 大型项目的可维护性

---

**问题**: Vtron框架是什么？为什么要用它来实现类似Windows 10的界面？

**考察重点**: 对桌面化Web应用的理解

**期望答案解析**:
- Vtron是一个基于Vue的桌面化UI框架
- 提供窗口管理、任务栏、桌面等桌面系统组件
- 适合开发复杂的企业级管理系统
- 用户体验更接近传统桌面应用

---

**3. 具体实现细节**

**问题**: 你提到"支持主窗口与多个子窗口的灵活管理"，这个功能是怎么实现的？

**技术实现思路**:
```javascript
// 窗口管理状态
const windowStore = {
  windows: [],
  activeWindowId: null,
  zIndexCounter: 1000,
  
  // 创建窗口
  createWindow(config) {
    const window = {
      id: generateId(),
      title: config.title,
      component: config.component,
      position: { x: 100, y: 100 },
      size: { width: 800, height: 600 },
      zIndex: ++this.zIndexCounter,
      minimized: false,
      maximized: false
    };
    this.windows.push(window);
    this.activeWindowId = window.id;
    return window;
  },
  
  // 销毁窗口
  destroyWindow(id) {
    const index = this.windows.findIndex(w => w.id === id);
    if (index > -1) {
      this.windows.splice(index, 1);
    }
  }
};
```

---

**问题**: 动态创建、销毁窗口时，如何处理内存管理和性能优化？

**标准答案**:
- **内存管理**:
  - 窗口关闭时清理组件实例
  - 移除事件监听器
  - 清理定时器和异步任务
  - 使用WeakMap存储窗口相关数据

- **性能优化**:
  - 窗口最小化时暂停不必要的更新
  - 使用虚拟滚动处理大量数据
  - 窗口池复用减少创建销毁开销
  - 懒加载窗口内容

---

**问题**: 组合式API中的ref、reactive有什么区别？什么时候用哪个？

**标准答案**:
- **ref**:
  - 用于基本数据类型和单个值
  - 通过.value访问和修改
  - 可以重新赋值整个对象

- **reactive**:
  - 用于对象和数组
  - 直接访问属性，不需要.value
  - 不能重新赋值整个对象，会失去响应性

```javascript
// ref 用法
const count = ref(0);
const user = ref({ name: 'Tom' });
count.value++; // 需要 .value
user.value = { name: 'Jerry' }; // 可以重新赋值

// reactive 用法
const state = reactive({
  count: 0,
  user: { name: 'Tom' }
});
state.count++; // 直接访问
// state = { count: 1 }; // ❌ 这样会失去响应性
```

**使用建议**:
- 基本类型用ref
- 对象类型优先考虑reactive，除非需要整体替换
- 组件props用ref包装后使用

---

**4. 路由和权限**

**问题**: Vue Router的路由守卫有哪几种？你在项目中用了哪些？

**标准答案**:
- **全局守卫**:
  - beforeEach: 全局前置守卫
  - beforeResolve: 全局解析守卫
  - afterEach: 全局后置钩子

- **路由独享守卫**:
  - beforeEnter: 路由配置中定义

- **组件内守卫**:
  - beforeRouteEnter: 进入路由前
  - beforeRouteUpdate: 路由更新时
  - beforeRouteLeave: 离开路由前

```javascript
// 全局前置守卫示例
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token');
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      next('/login');
    } else {
      next();
    }
  } else {
    next();
  }
});
```

---

**问题**: 前端权限控制的实现思路是什么？

**标准答案**:
- **路由级权限**: 通过路由守卫控制页面访问
- **组件级权限**: 通过指令或组件控制UI显示
- **接口级权限**: 请求拦截器添加token，响应拦截器处理401

```javascript
// 权限指令
const permission = {
  mounted(el, binding) {
    const { value } = binding;
    const permissions = store.state.user.permissions;
    
    if (!permissions.includes(value)) {
      el.style.display = 'none';
    }
  }
};

// 使用
<button v-permission="'user:delete'">删除用户</button>
```

---

#### 针对AI Agent项目

**问题**: 你在这个项目中主要负责什么前端工作？

**考察重点**: 前端如何与AI服务交互

**期望答案**:
- 聊天界面开发
- 流式数据渲染
- 消息状态管理
- 文件上传和预览功能

---

**问题**: SSE流式响应是什么？为什么要用这种方式？

**标准答案**:
- **SSE (Server-Sent Events)**:
  - 服务器向客户端推送数据的技术
  - 基于HTTP协议，单向通信
  - 支持自动重连和事件类型

- **使用原因**:
  - AI生成内容需要逐步展示
  - 提升用户体验，避免长时间等待
  - 减少服务器压力，避免长连接阻塞

```javascript
// SSE 客户端实现
function createSSEConnection(url) {
  const eventSource = new EventSource(url);
  
  eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // 逐步更新UI内容
    appendToChat(data.content);
  };
  
  eventSource.onerror = function(event) {
    console.error('SSE connection error:', event);
    // 处理连接错误
  };
  
  return eventSource;
}
```

---

### 第四部分：工程化与工具 (8分钟)

**1. 构建工具**

**问题**: Vite相比Webpack有什么优势？

**标准答案**:
- **开发体验**:
  - 冷启动速度更快（基于ESM）
  - 热更新效率更高
  - 内置TypeScript支持

- **构建性能**:
  - 使用Rollup打包，体积更小
  - Tree-shaking效果更好
  - 并行构建支持

- **配置简化**:
  - 开箱即用，配置更少
  - 插件生态丰富
  - 现代化的默认配置

**使用场景**:
- 新项目优先选择Vite
- Vue3/React等现代框架项目
- 需要快速开发原型的场景

---

**问题**: 前端项目的打包优化策略有哪些？

**标准答案**:
- **代码分割**:
  - 路由懒加载
  - 第三方库单独打包
  - 动态import()

- **资源优化**:
  - 图片压缩和WebP格式
  - CSS/JS压缩
  - Gzip压缩

- **缓存策略**:
  - 文件名hash化
  - 合理设置缓存头
  - CDN部署

```javascript
// Vite 打包配置示例
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          ui: ['element-plus']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
});
```

---

**2. 开发规范**

**问题**: 如何保证代码质量？用过哪些工具？

**标准答案**:
- **代码规范工具**:
  - ESLint: 代码语法检查
  - Prettier: 代码格式化
  - Stylelint: CSS代码检查

- **类型检查**:
  - TypeScript: 静态类型检查
  - Vue Tsc: Vue文件类型检查

- **提交规范**:
  - Husky: Git hooks管理
  - Lint-staged: 暂存文件检查
  - Commitizen: 提交信息规范

```json
// package.json 配置示例
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{js,vue,ts}": ["eslint --fix", "git add"]
  }
}
```

---

**问题**: Git的工作流程和分支管理策略？

**标准答案**:
- **Git Flow**:
  - master: 生产分支
  - develop: 开发分支
  - feature/*: 功能分支
  - release/*: 发布分支
  - hotfix/*: 热修复分支

- **工作流程**:
  1. 从develop创建feature分支
  2. 完成功能后提交PR
  3. 代码审查通过后合并到develop
  4. 测试通过后合并到master发布

- **最佳实践**:
  - 提交信息规范化
  - 小步快跑，频繁提交
  - 合并前rebase保持历史整洁

---

### 第五部分：问答环节 (2分钟)
**问题**: 你有什么问题要问我的吗？

**考察重点**: 
- 对公司和岗位的关注程度
- 职业规划和学习态度
- 沟通能力

---
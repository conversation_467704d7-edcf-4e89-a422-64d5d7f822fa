<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校招 - 梁琦坤 - 2025.08.11</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .interview-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .interview-info div {
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        /* 导航栏样式 */
        .navigation {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 2px solid #4facfe;
            z-index: 1000;
        }
        
        .nav-sections {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            padding: 8px 16px;
            background: #f8f9ff;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
            transform: translateY(-2px);
        }
        
        .nav-btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #4facfe;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-btn.active::after,
        .nav-btn:hover::after {
            width: 100%;
        }
        
        .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .candidate-info {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .candidate-input {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            min-width: 150px;
            transition: all 0.3s ease;
        }
        
        .candidate-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .score-display {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            padding: 12px 24px;
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 25px;
            min-width: 140px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        
        .progress-container {
            margin-top: 15px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e1e8ed;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 5px;
        }
        
        /* 章节样式 */
        .section {
            margin-bottom: 40px;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #4facfe;
            scroll-margin-top: 120px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e8ed;
        }
        
        .section-subtitle {
            color: #4facfe;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-subtitle::before {
            content: "▶";
            font-size: 0.8em;
        }
        
        /* 问题样式 */
        .question {
            margin-bottom: 25px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .question:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .question-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #4facfe;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            font-weight: bold;
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }
        
        .question:hover .question-number {
            transform: scale(1.1);
        }
        
        .question-text {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
            line-height: 1.5;
        }
        
        .answer-reference {
            font-size: 0.9em;
            color: #666;
            background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 3px solid #4facfe;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference-header {
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(79, 172, 254, 0.05);
            border-bottom: 1px solid rgba(79, 172, 254, 0.1);
        }

        .answer-reference-header:hover {
            background: rgba(79, 172, 254, 0.08);
        }

        .answer-reference-title {
            font-weight: 600;
            color: #4facfe;
            font-size: 0.85em;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .answer-reference-toggle {
            font-size: 0.8em;
            color: #999;
            transition: transform 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-toggle {
            transform: rotate(180deg);
        }

        .answer-reference-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-content {
            max-height: 500px;
            padding: 12px 15px;
        }

        .answer-reference strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 6px;
        }
        
        .input-group {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .answer-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1em;
            min-height: 100px;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .answer-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
        }
        
        .score-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            min-width: 80px;
        }
        
        .score-input {
            width: 70px;
            height: 50px;
            padding: 8px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .score-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            transform: scale(1.05);
        }
        
        .score-input.invalid {
            border-color: #ff6b6b;
            background-color: #ffe0e0;
        }
        
        .score-label {
            font-size: 0.85em;
            color: #666;
            text-align: center;
            font-weight: 600;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                gap: 15px;
            }
            
            .score-group {
                flex-direction: row;
                justify-content: center;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>校招 - 梁琦坤 -2025.08.11</h1>
            <div class="interview-info">
                <div><strong>👨‍💼 面试官：</strong>左浩</div>
                <div><strong>⏱️ 面试时长：</strong>60分钟</div>
                <div><strong>💻 面试方式：</strong>远程视频面试</div>
            </div>
        </div>
        
        <div class="content">
            <div class="navigation">
                <div class="nav-sections">
                    <a href="#section-opening" class="nav-btn">开场&自我介绍</a>
                    <a href="#section-core" class="nav-btn">核心技术能力</a>
                    <a href="#section-vue" class="nav-btn">Vue框架理解</a>
                    <a href="#section-project" class="nav-btn">项目经验</a>
                    <a href="#section-vision" class="nav-btn">技术视野</a>
                </div>
                <div class="controls-row">
                    <div class="candidate-info">
                        <label>候选人姓名：</label>
                        <input type="text" id="candidateName" class="candidate-input" placeholder="许同学" required>
                        <label>面试日期：</label>
                        <input type="date" id="interviewDate" class="candidate-input" required>
                    </div>
                    <div class="score-display">
                        平均分：<span id="averageScore">0.0</span>/5.0
                    </div>
                    <div class="action-buttons">
                        <button onclick="autoSave()" class="btn btn-secondary">💾 保存进度</button>
                        <button onclick="exportToExcel()" class="btn btn-primary">📊 导出Excel</button>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-info">
                        <span>完成进度</span>
                        <span id="progressText">0/20 题</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- 开场 & 自我介绍 -->
            <div class="section" id="section-opening">
                <h2 class="section-title">🎯 开场 & 自我介绍（4分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">1</div>
                        <div class="question-text">面试官开场白（1分钟）</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考内容
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考内容：</strong>你好，同学！很高兴见到你，我是今天的面试官左浩。谢谢你抽时间来聊，咱们今天就轻松一点，当作一次技术交流。准备好了就开始吧。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人反应和回应..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">2</div>
                        <div class="question-text">候选人自我介绍（3分钟）- 请先做个自我介绍</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                📝 评分要点
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>评分要点：</strong>表达逻辑清晰、技术背景介绍、项目经验相关性、个人特点突出
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的自我介绍内容..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第一部分：核心技术能力 -->
            <div class="section" id="section-core">
                <h2 class="section-title">🔧 第一部分：核心技术能力（18分钟）</h2>

                <h3 class="section-subtitle">JavaScript 基础（6分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">3</div>
                        <div class="question-text">什么是闭包？你在项目中用过闭包的场景吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>定义：</strong>闭包是指函数可以访问其外部函数作用域中的变量，即使外部函数已经执行结束。<br>
                            <strong>原理：</strong>函数在创建时会绑定词法作用域，形成作用域链。<br>
                            <strong>加分点：</strong>提到闭包可能引发内存泄漏（未释放的引用）；能结合实际项目举例。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">4</div>
                        <div class="question-text">var、let、const 区别？什么时候用哪种？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>var：</strong>函数作用域、变量提升、可重复声明。<br>
                            <strong>let：</strong>块级作用域、不可重复声明、不提升到可用前（暂时性死区）。<br>
                            <strong>const：</strong>块级作用域、必须初始化、不能重新赋值（对象和数组的引用可变）。<br>
                            <strong>推荐：</strong>默认 const，需要修改的用 let，避免用 var。<br>
                            <strong>加分点：</strong>提到 const 修饰对象时只是引用不可变。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">5</div>
                        <div class="question-text">进阶题：你写了一个工具方法返回另一个函数，调用时读取内部变量的值，但这个变量一直没释放，导致内存增长。为什么会这样？怎么优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>原因：</strong>闭包持有变量引用，GC 无法回收。<br>
                            <strong>优化方法：</strong>不再需要时将变量设为 null；减少闭包嵌套范围；在大型数据使用完后手动释放。<br>
                            <strong>加分点：</strong>提到 Chrome Performance 面板可观察内存曲线。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">异步 & 事件循环（6分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">6</div>
                        <div class="question-text">Promise 和 async/await 的区别？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>Promise：</strong>链式调用 .then / .catch，代码可读性差时嵌套深。<br>
                            <strong>async/await：</strong>基于 Promise 的语法糖，像同步写法，更易维护。<br>
                            <strong>错误处理：</strong>Promise 用 .catch；async/await 用 try/catch。<br>
                            <strong>加分点：</strong>async/await 会暂停函数执行，释放调用栈。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">7</div>
                        <div class="question-text">什么是事件循环？宏任务 & 微任务的执行顺序？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>事件循环：</strong>JS 是单线程的，通过事件循环机制处理同步任务和异步任务。<br>
                            <strong>宏任务：</strong>script、setTimeout、setInterval、I/O 等。<br>
                            <strong>微任务：</strong>Promise.then、MutationObserver、queueMicrotask。<br>
                            <strong>执行顺序：</strong>宏任务 → 执行完所有微任务 → 下一个宏任务。<br>
                            <strong>加分点：</strong>能画一个事件循环的执行顺序示意。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">8</div>
                        <div class="question-text">进阶题：你做了一个数据轮询功能，每 2 秒请求一次接口，但接口响应慢时会延迟或重叠。怎么改进？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>问题原因：</strong>使用 setInterval，任务可能堆积。<br>
                            <strong>解决方案：</strong>使用递归 setTimeout，在上一次请求完成后再延时；或使用 Promise 链式调用。<br>
                            <strong>加分点：</strong>提到可用 AbortController 中断上一次未完成的请求。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">HTTP & 浏览器基础（6分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">9</div>
                        <div class="question-text">GET & POST 区别？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>GET：</strong>幂等，数据放在 URL，长度有限，适合查询。<br>
                            <strong>POST：</strong>不幂等，数据放在 body，无长度限制，适合提交数据。<br>
                            <strong>加分点：</strong>提到 HTTP 规范中 GET/POST 并不限定 body，只是约定。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">10</div>
                        <div class="question-text">强缓存和协商缓存的原理？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>强缓存：</strong>Expires / Cache-Control，命中时直接用本地缓存，不发请求。<br>
                            <strong>协商缓存：</strong>Last-Modified / ETag，发请求询问服务器是否有更新。<br>
                            <strong>加分点：</strong>提到 HTTP/2 可能减少协商缓存的性能损耗。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">11</div>
                        <div class="question-text">进阶题：如果一个页面需要首屏秒开，你会怎么利用缓存 & 预加载？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>缓存策略：</strong>静态资源使用强缓存，更新用文件名 hash；关键数据使用 localStorage/sessionStorage 缓存。<br>
                            <strong>预加载：</strong>使用 &lt;link rel="preload"&gt; 预加载关键资源。<br>
                            <strong>加分点：</strong>提到骨架屏、懒加载结合缓存。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二部分：Vue 框架理解 -->
            <div class="section" id="section-vue">
                <h2 class="section-title">⚛️ 第二部分：Vue 框架理解（15分钟）</h2>

                <h3 class="section-subtitle">Vue 核心概念（7分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">12</div>
                        <div class="question-text">Vue 响应式原理？Vue 2 和 Vue 3 在实现上有何不同？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>Vue 2：</strong>基于 Object.defineProperty，只能监听已存在的属性。<br>
                            <strong>Vue 3：</strong>基于 Proxy，可监听对象和数组所有操作。<br>
                            <strong>加分点：</strong>提到 Vue 3 响应式支持弱引用（WeakMap）防止内存泄漏。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">13</div>
                        <div class="question-text">父子组件通信有哪些方式？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>通信方式：</strong>props / emit；provide / inject；Vuex / Pinia（全局状态）；EventBus。<br>
                            <strong>加分点：</strong>提到 URL 参数、localStorage 等非直接通信方式。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">14</div>
                        <div class="question-text">Composition API 相比 Options API 有什么优势？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>优势：</strong>更灵活的逻辑复用（通过组合函数）；更好的 TypeScript 支持；更清晰的逻辑组织。<br>
                            <strong>加分点：</strong>提到复杂组件中减少 this 的使用带来的好处。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">Vue 场景题（8分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">15</div>
                        <div class="question-text">页面中有一个长列表组件渲染很慢，你会怎么优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>优化方法：</strong>虚拟列表（只渲染可视区）；分批渲染（requestIdleCallback / 分片渲染）；组件懒加载。<br>
                            <strong>加分点：</strong>提到图片懒加载、服务端分页。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">16</div>
                        <div class="question-text">如果一个组件状态管理越来越复杂，你会如何拆分？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>拆分方法：</strong>按功能拆分成多个子组件；提取公共逻辑到 composable 或 store。<br>
                            <strong>加分点：</strong>提到"容器组件 + 展示组件"模式。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">17</div>
                        <div class="question-text">进阶题：虚拟 DOM 为什么能提升性能？有没有场景它反而会更慢？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>提升性能：</strong>减少真实 DOM 操作，批量更新。<br>
                            <strong>可能更慢：</strong>节点频繁变动、小规模静态页面（额外 diff 成本）。<br>
                            <strong>加分点：</strong>提到 Vue 3 支持 v-once、static 节点优化。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三部分：项目经验 & 场景化考察 -->
            <div class="section" id="section-project">
                <h2 class="section-title">💼 第三部分：项目经验 & 场景化考察（18分钟）</h2>

                <h3 class="section-subtitle">i-did 智能平台</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">18</div>
                        <div class="question-text">你提到加载效率提升 40%，瓶颈在哪里？怎么定位和解决的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>可能瓶颈：</strong>首屏资源过大、接口响应慢。<br>
                            <strong>定位方法：</strong>Chrome Performance / Lighthouse 分析。<br>
                            <strong>解决方案：</strong>资源压缩、懒加载、缓存优化。<br>
                            <strong>加分点：</strong>提到使用 Webpack Bundle Analyzer 定位打包问题。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">19</div>
                        <div class="question-text">如果再优化一次，你会做哪些不同的尝试？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>优化方向：</strong>引入 HTTP/2、预加载关键资源；使用 SSR 或 SSG。<br>
                            <strong>加分点：</strong>提到监控首屏加载指标（FP、LCP、TTI）。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四部分：技术视野与成长 -->
            <div class="section" id="section-vision">
                <h2 class="section-title">🚀 第四部分：技术视野与成长（5分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">20</div>
                        <div class="question-text">如果让你选一个前端框架做新项目，你会考虑哪些因素？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>考虑因素：</strong>团队技术栈熟悉度；生态支持；性能 & 维护成本。<br>
                            <strong>加分点：</strong>提到可扩展性与社区活跃度。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">21</div>
                        <div class="question-text">你平时怎么学习新技术的？最近学了什么？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>学习渠道：</strong>官方文档、社区文章、视频课程。<br>
                            <strong>最近学习内容举例。</strong><br>
                            <strong>加分点：</strong>提到结合实际项目验证新技术。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">22</div>
                        <div class="question-text">对我们团队或岗位有什么想了解的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考解析
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考解析：</strong><br>
                            <strong>关注团队技术氛围、发展方向。</strong><br>
                            <strong>加分点：</strong>问到业务核心挑战，显示求知欲。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自动保存功能
        function autoSave() {
            const data = {
                candidateName: document.getElementById('candidateName').value,
                interviewDate: document.getElementById('interviewDate').value,
                answers: [],
                scores: []
            };

            // 收集所有答案和分数
            const answerInputs = document.querySelectorAll('.answer-input');
            const scoreInputs = document.querySelectorAll('.score-input');

            answerInputs.forEach((input, index) => {
                data.answers[index] = input.value;
            });

            scoreInputs.forEach((input, index) => {
                data.scores[index] = input.value;
            });

            localStorage.setItem('interviewData', JSON.stringify(data));
            alert('数据已保存到本地存储！');
        }

        // 加载保存的数据
        function loadSavedData() {
            const savedData = localStorage.getItem('interviewData');
            if (savedData) {
                const data = JSON.parse(savedData);

                if (data.candidateName) {
                    document.getElementById('candidateName').value = data.candidateName;
                }
                if (data.interviewDate) {
                    document.getElementById('interviewDate').value = data.interviewDate;
                }

                const answerInputs = document.querySelectorAll('.answer-input');
                const scoreInputs = document.querySelectorAll('.score-input');

                data.answers?.forEach((answer, index) => {
                    if (answerInputs[index]) {
                        answerInputs[index].value = answer;
                    }
                });

                data.scores?.forEach((score, index) => {
                    if (scoreInputs[index]) {
                        scoreInputs[index].value = score;
                    }
                });

                calculateAverage();
            }
        }

        // 切换参考解析显示/隐藏
        function toggleReference(element) {
            const reference = element.closest('.answer-reference');
            reference.classList.toggle('expanded');
        }

        // 计算平均分
        function calculateAverage() {
            const scoreInputs = document.querySelectorAll('.score-input');
            let total = 0;
            let count = 0;
            let filledCount = 0;

            scoreInputs.forEach(input => {
                count++;
                if (input.value && input.value !== '') {
                    total += parseFloat(input.value);
                    filledCount++;
                }
            });

            const average = filledCount > 0 ? (total / filledCount).toFixed(1) : '0.0';
            document.getElementById('averageScore').textContent = average;

            // 更新进度
            const progressPercent = (filledCount / count) * 100;
            document.getElementById('progressFill').style.width = progressPercent + '%';
            document.getElementById('progressText').textContent = `${filledCount}/${count} 题`;

            // 自动保存
            autoSave();
        }

        // 验证分数输入
        function validateScore(input) {
            const value = parseFloat(input.value);
            if (isNaN(value) || value < 1 || value > 5) {
                input.classList.add('invalid');
            } else {
                input.classList.remove('invalid');
            }
        }

        // 导出到Excel
        function exportToExcel() {
            const candidateName = document.getElementById('candidateName').value || '候选人';
            const interviewDate = document.getElementById('interviewDate').value || new Date().toISOString().split('T')[0];

            const data = [];
            const questions = document.querySelectorAll('.question');

            questions.forEach((question, index) => {
                const questionText = question.querySelector('.question-text').textContent;
                const answer = question.querySelector('.answer-input').value;
                const score = question.querySelector('.score-input').value;

                data.push({
                    '题号': index + 1,
                    '问题': questionText,
                    '候选人回答': answer,
                    '分数': score
                });
            });

            // 添加总结行
            const averageScore = document.getElementById('averageScore').textContent;
            data.push({
                '题号': '',
                '问题': '平均分',
                '候选人回答': '',
                '分数': averageScore
            });

            const ws = XLSX.utils.json_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "面试评估");

            const fileName = `${candidateName}_面试评估_${interviewDate}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('interviewDate').value = today;

            // 加载保存的数据
            loadSavedData();

            // 导航功能
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有active类
                    navBtns.forEach(b => b.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');

                    // 滚动到对应部分
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 监听滚动，更新导航状态
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.section');
                const scrollPos = window.scrollY + 150;

                sections.forEach(section => {
                    const top = section.offsetTop;
                    const bottom = top + section.offsetHeight;
                    const id = section.getAttribute('id');

                    if (scrollPos >= top && scrollPos <= bottom) {
                        navBtns.forEach(btn => {
                            btn.classList.remove('active');
                            if (btn.getAttribute('href') === `#${id}`) {
                                btn.classList.add('active');
                            }
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
